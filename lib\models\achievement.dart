enum AchievementType {
  score,
  streak,
  category,
  accuracy,
  speed,
  participation,
}

enum AchievementRarity {
  common,
  rare,
  epic,
  legendary,
}

class Achievement {
  final String id;
  final String title;
  final String description;
  final String icon;
  final AchievementType type;
  final AchievementRarity rarity;
  final int targetValue;
  final int currentProgress;
  final bool isUnlocked;
  final DateTime? unlockedAt;
  final int coinsReward;
  final String category;

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.type,
    required this.rarity,
    required this.targetValue,
    this.currentProgress = 0,
    this.isUnlocked = false,
    this.unlockedAt,
    this.coinsReward = 0,
    this.category = '',
  });

  // Calculate progress percentage
  double get progressPercentage {
    if (targetValue == 0) return 0.0;
    return (currentProgress / targetValue).clamp(0.0, 1.0);
  }

  // Check if achievement is completed but not unlocked
  bool get isCompleted => currentProgress >= targetValue;

  // Get rarity color
  String get rarityColor {
    switch (rarity) {
      case AchievementRarity.common:
        return 'grey';
      case AchievementRarity.rare:
        return 'blue';
      case AchievementRarity.epic:
        return 'purple';
      case AchievementRarity.legendary:
        return 'orange';
    }
  }

  // Get rarity name
  String get rarityName {
    switch (rarity) {
      case AchievementRarity.common:
        return 'Common';
      case AchievementRarity.rare:
        return 'Rare';
      case AchievementRarity.epic:
        return 'Epic';
      case AchievementRarity.legendary:
        return 'Legendary';
    }
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'icon': icon,
      'type': type.toString(),
      'rarity': rarity.toString(),
      'targetValue': targetValue,
      'currentProgress': currentProgress,
      'isUnlocked': isUnlocked,
      'unlockedAt': unlockedAt?.toIso8601String(),
      'coinsReward': coinsReward,
      'category': category,
    };
  }

  // Create from JSON
  factory Achievement.fromJson(Map<String, dynamic> json) {
    return Achievement(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      icon: json['icon'],
      type: AchievementType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => AchievementType.score,
      ),
      rarity: AchievementRarity.values.firstWhere(
        (e) => e.toString() == json['rarity'],
        orElse: () => AchievementRarity.common,
      ),
      targetValue: json['targetValue'],
      currentProgress: json['currentProgress'] ?? 0,
      isUnlocked: json['isUnlocked'] ?? false,
      unlockedAt: json['unlockedAt'] != null 
          ? DateTime.parse(json['unlockedAt']) 
          : null,
      coinsReward: json['coinsReward'] ?? 0,
      category: json['category'] ?? '',
    );
  }

  // Copy with method for updates
  Achievement copyWith({
    String? id,
    String? title,
    String? description,
    String? icon,
    AchievementType? type,
    AchievementRarity? rarity,
    int? targetValue,
    int? currentProgress,
    bool? isUnlocked,
    DateTime? unlockedAt,
    int? coinsReward,
    String? category,
  }) {
    return Achievement(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      type: type ?? this.type,
      rarity: rarity ?? this.rarity,
      targetValue: targetValue ?? this.targetValue,
      currentProgress: currentProgress ?? this.currentProgress,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      unlockedAt: unlockedAt ?? this.unlockedAt,
      coinsReward: coinsReward ?? this.coinsReward,
      category: category ?? this.category,
    );
  }

  // Static method to create default achievements
  static List<Achievement> getDefaultAchievements() {
    return [
      Achievement(
        id: 'first_quiz',
        title: 'First Steps',
        description: 'Complete your first quiz',
        icon: '🎯',
        type: AchievementType.participation,
        rarity: AchievementRarity.common,
        targetValue: 1,
        coinsReward: 10,
      ),
      Achievement(
        id: 'perfect_score',
        title: 'Perfect Score',
        description: 'Get 100% on any quiz',
        icon: '💯',
        type: AchievementType.accuracy,
        rarity: AchievementRarity.rare,
        targetValue: 1,
        coinsReward: 50,
      ),
      Achievement(
        id: 'streak_master',
        title: 'Streak Master',
        description: 'Maintain a 7-day daily quiz streak',
        icon: '🔥',
        type: AchievementType.streak,
        rarity: AchievementRarity.epic,
        targetValue: 7,
        coinsReward: 100,
      ),
      Achievement(
        id: 'high_scorer',
        title: 'High Scorer',
        description: 'Reach 1000 total points',
        icon: '⭐',
        type: AchievementType.score,
        rarity: AchievementRarity.rare,
        targetValue: 1000,
        coinsReward: 75,
      ),
      Achievement(
        id: 'quiz_legend',
        title: 'Quiz Legend',
        description: 'Reach 5000 total points',
        icon: '👑',
        type: AchievementType.score,
        rarity: AchievementRarity.legendary,
        targetValue: 5000,
        coinsReward: 200,
      ),
      Achievement(
        id: 'speed_demon',
        title: 'Speed Demon',
        description: 'Complete a quiz in under 2 minutes',
        icon: '⚡',
        type: AchievementType.speed,
        rarity: AchievementRarity.epic,
        targetValue: 120, // 2 minutes in seconds
        coinsReward: 80,
      ),
      Achievement(
        id: 'category_explorer',
        title: 'Category Explorer',
        description: 'Complete quizzes in all categories',
        icon: '🗺️',
        type: AchievementType.category,
        rarity: AchievementRarity.epic,
        targetValue: 5, // Assuming 5 categories
        coinsReward: 150,
      ),
      Achievement(
        id: 'consistent_player',
        title: 'Consistent Player',
        description: 'Complete 50 quizzes',
        icon: '📚',
        type: AchievementType.participation,
        rarity: AchievementRarity.rare,
        targetValue: 50,
        coinsReward: 100,
      ),
    ];
  }
}
