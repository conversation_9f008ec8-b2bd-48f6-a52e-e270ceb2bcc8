import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '/models/achievement.dart';
import '/controllers/data_controller.dart';

class AchievementController extends GetxController {
  final RxList<Achievement> _achievements = <Achievement>[].obs;
  final RxBool _isLoading = false.obs;
  final RxList<Achievement> _recentlyUnlocked = <Achievement>[].obs;

  // Getters
  List<Achievement> get achievements => _achievements;
  bool get isLoading => _isLoading.value;
  List<Achievement> get recentlyUnlocked => _recentlyUnlocked;

  // Filter getters
  List<Achievement> get unlockedAchievements => 
      _achievements.where((a) => a.isUnlocked).toList();
  
  List<Achievement> get lockedAchievements => 
      _achievements.where((a) => !a.isUnlocked).toList();

  List<Achievement> get completedButNotUnlocked => 
      _achievements.where((a) => a.isCompleted && !a.isUnlocked).toList();

  @override
  void onInit() {
    super.onInit();
    loadAchievements();
  }

  // Load achievements from SharedPreferences
  Future<void> loadAchievements() async {
    _isLoading.value = true;
    try {
      final prefs = await SharedPreferences.getInstance();
      final achievementsJson = prefs.getStringList('achievements');
      
      if (achievementsJson != null && achievementsJson.isNotEmpty) {
        _achievements.value = achievementsJson
            .map((json) => Achievement.fromJson(jsonDecode(json)))
            .toList();
      } else {
        // Initialize with default achievements
        _achievements.value = Achievement.getDefaultAchievements();
        await saveAchievements();
      }
    } catch (e) {
      print('Error loading achievements: $e');
      // Fallback to default achievements
      _achievements.value = Achievement.getDefaultAchievements();
    } finally {
      _isLoading.value = false;
    }
  }

  // Save achievements to SharedPreferences
  Future<void> saveAchievements() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final achievementsJson = _achievements
          .map((achievement) => jsonEncode(achievement.toJson()))
          .toList();
      
      await prefs.setStringList('achievements', achievementsJson);
    } catch (e) {
      print('Error saving achievements: $e');
    }
  }

  // Update achievement progress
  Future<void> updateProgress(String achievementId, int progress) async {
    final index = _achievements.indexWhere((a) => a.id == achievementId);
    if (index != -1) {
      final achievement = _achievements[index];
      final updatedAchievement = achievement.copyWith(
        currentProgress: progress,
      );
      
      _achievements[index] = updatedAchievement;
      
      // Check if achievement should be unlocked
      if (updatedAchievement.isCompleted && !updatedAchievement.isUnlocked) {
        await unlockAchievement(achievementId);
      }
      
      await saveAchievements();
    }
  }

  // Unlock achievement
  Future<void> unlockAchievement(String achievementId) async {
    final index = _achievements.indexWhere((a) => a.id == achievementId);
    if (index != -1) {
      final achievement = _achievements[index];
      if (!achievement.isUnlocked) {
        final unlockedAchievement = achievement.copyWith(
          isUnlocked: true,
          unlockedAt: DateTime.now(),
        );
        
        _achievements[index] = unlockedAchievement;
        _recentlyUnlocked.add(unlockedAchievement);
        
        // Award coins to user
        final dataController = Get.find<DataController>();
        dataController.coins.value += achievement.coinsReward;
        await dataController.saveUserData();
        
        await saveAchievements();
        
        // Show achievement notification
        _showAchievementNotification(unlockedAchievement);
      }
    }
  }

  // Check and update achievements based on user actions
  Future<void> checkAchievements({
    int? totalScore,
    int? gamesPlayed,
    int? dailyStreak,
    String? category,
    double? accuracy,
    int? quizTime,
    bool? perfectScore,
  }) async {
    // Update score-based achievements
    if (totalScore != null) {
      await updateProgress('high_scorer', totalScore);
      await updateProgress('quiz_legend', totalScore);
    }

    // Update participation achievements
    if (gamesPlayed != null) {
      await updateProgress('first_quiz', gamesPlayed);
      await updateProgress('consistent_player', gamesPlayed);
    }

    // Update streak achievements
    if (dailyStreak != null) {
      await updateProgress('streak_master', dailyStreak);
    }

    // Update accuracy achievements
    if (perfectScore == true) {
      await updateProgress('perfect_score', 1);
    }

    // Update speed achievements
    if (quizTime != null && quizTime <= 120) { // 2 minutes
      await updateProgress('speed_demon', 1);
    }

    // Update category exploration
    if (category != null) {
      final completedCategories = await _getCompletedCategories();
      await updateProgress('category_explorer', completedCategories.length);
    }
  }

  // Get completed categories from quiz history
  Future<Set<String>> _getCompletedCategories() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getStringList('quiz_history') ?? [];
      
      final categories = <String>{};
      for (final json in historyJson) {
        final data = jsonDecode(json);
        categories.add(data['category']);
      }
      
      return categories;
    } catch (e) {
      return <String>{};
    }
  }

  // Show achievement notification
  void _showAchievementNotification(Achievement achievement) {
    Get.snackbar(
      '🎉 Achievement Unlocked!',
      '${achievement.title}\n+${achievement.coinsReward} coins',
      backgroundColor: _getRarityColor(achievement.rarity),
      colorText: Colors.white,
      duration: const Duration(seconds: 4),
      snackPosition: SnackPosition.TOP,
      icon: Text(
        achievement.icon,
        style: const TextStyle(fontSize: 24),
      ),
    );
  }

  // Get rarity color
  Color _getRarityColor(AchievementRarity rarity) {
    switch (rarity) {
      case AchievementRarity.common:
        return Colors.grey;
      case AchievementRarity.rare:
        return Colors.blue;
      case AchievementRarity.epic:
        return Colors.purple;
      case AchievementRarity.legendary:
        return Colors.orange;
    }
  }

  // Get achievements by rarity
  List<Achievement> getAchievementsByRarity(AchievementRarity rarity) {
    return _achievements.where((a) => a.rarity == rarity).toList();
  }

  // Get achievements by type
  List<Achievement> getAchievementsByType(AchievementType type) {
    return _achievements.where((a) => a.type == type).toList();
  }

  // Get achievement statistics
  Map<String, dynamic> getStatistics() {
    final total = _achievements.length;
    final unlocked = unlockedAchievements.length;
    final totalCoinsEarned = unlockedAchievements
        .fold<int>(0, (sum, achievement) => sum + achievement.coinsReward);
    
    final rarityStats = <String, int>{};
    for (final rarity in AchievementRarity.values) {
      final count = unlockedAchievements
          .where((a) => a.rarity == rarity)
          .length;
      rarityStats[rarity.toString().split('.').last] = count;
    }

    return {
      'total': total,
      'unlocked': unlocked,
      'locked': total - unlocked,
      'completionPercentage': (unlocked / total) * 100,
      'totalCoinsEarned': totalCoinsEarned,
      'rarityStats': rarityStats,
    };
  }

  // Clear recently unlocked achievements
  void clearRecentlyUnlocked() {
    _recentlyUnlocked.clear();
  }

  // Reset all achievements (for testing)
  Future<void> resetAchievements() async {
    _achievements.value = Achievement.getDefaultAchievements();
    _recentlyUnlocked.clear();
    await saveAchievements();
  }
}
