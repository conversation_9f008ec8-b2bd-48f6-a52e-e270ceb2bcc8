class QuizHistory {
  final String id;
  final String category;
  final int score;
  final int totalQuestions;
  final double accuracy;
  final DateTime completedAt;
  final Duration timeTaken;
  final bool isDailyQuiz;
  final int coinsEarned;
  final List<QuestionResult> questionResults;

  QuizHistory({
    required this.id,
    required this.category,
    required this.score,
    required this.totalQuestions,
    required this.accuracy,
    required this.completedAt,
    required this.timeTaken,
    this.isDailyQuiz = false,
    this.coinsEarned = 0,
    this.questionResults = const [],
  });

  // Calculate percentage score
  double get percentage => (score / totalQuestions) * 100;

  // Get performance level
  String get performanceLevel {
    if (percentage >= 90) return 'Excellent';
    if (percentage >= 80) return 'Great';
    if (percentage >= 70) return 'Good';
    if (percentage >= 60) return 'Average';
    return 'Needs Improvement';
  }

  // Get performance color
  String get performanceColor {
    if (percentage >= 90) return 'green';
    if (percentage >= 80) return 'blue';
    if (percentage >= 70) return 'orange';
    if (percentage >= 60) return 'yellow';
    return 'red';
  }

  // Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category,
      'score': score,
      'totalQuestions': totalQuestions,
      'accuracy': accuracy,
      'completedAt': completedAt.toIso8601String(),
      'timeTaken': timeTaken.inSeconds,
      'isDailyQuiz': isDailyQuiz,
      'coinsEarned': coinsEarned,
      'questionResults': questionResults.map((q) => q.toJson()).toList(),
    };
  }

  // Create from JSON
  factory QuizHistory.fromJson(Map<String, dynamic> json) {
    return QuizHistory(
      id: json['id'],
      category: json['category'],
      score: json['score'],
      totalQuestions: json['totalQuestions'],
      accuracy: json['accuracy']?.toDouble() ?? 0.0,
      completedAt: DateTime.parse(json['completedAt']),
      timeTaken: Duration(seconds: json['timeTaken']),
      isDailyQuiz: json['isDailyQuiz'] ?? false,
      coinsEarned: json['coinsEarned'] ?? 0,
      questionResults: (json['questionResults'] as List<dynamic>?)
          ?.map((q) => QuestionResult.fromJson(q))
          .toList() ?? [],
    );
  }

  // Copy with method for updates
  QuizHistory copyWith({
    String? id,
    String? category,
    int? score,
    int? totalQuestions,
    double? accuracy,
    DateTime? completedAt,
    Duration? timeTaken,
    bool? isDailyQuiz,
    int? coinsEarned,
    List<QuestionResult>? questionResults,
  }) {
    return QuizHistory(
      id: id ?? this.id,
      category: category ?? this.category,
      score: score ?? this.score,
      totalQuestions: totalQuestions ?? this.totalQuestions,
      accuracy: accuracy ?? this.accuracy,
      completedAt: completedAt ?? this.completedAt,
      timeTaken: timeTaken ?? this.timeTaken,
      isDailyQuiz: isDailyQuiz ?? this.isDailyQuiz,
      coinsEarned: coinsEarned ?? this.coinsEarned,
      questionResults: questionResults ?? this.questionResults,
    );
  }
}

class QuestionResult {
  final String question;
  final List<String> options;
  final int correctAnswerIndex;
  final int? selectedAnswerIndex;
  final bool isCorrect;
  final Duration timeSpent;

  QuestionResult({
    required this.question,
    required this.options,
    required this.correctAnswerIndex,
    this.selectedAnswerIndex,
    required this.isCorrect,
    required this.timeSpent,
  });

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'question': question,
      'options': options,
      'correctAnswerIndex': correctAnswerIndex,
      'selectedAnswerIndex': selectedAnswerIndex,
      'isCorrect': isCorrect,
      'timeSpent': timeSpent.inSeconds,
    };
  }

  // Create from JSON
  factory QuestionResult.fromJson(Map<String, dynamic> json) {
    return QuestionResult(
      question: json['question'],
      options: List<String>.from(json['options']),
      correctAnswerIndex: json['correctAnswerIndex'],
      selectedAnswerIndex: json['selectedAnswerIndex'],
      isCorrect: json['isCorrect'],
      timeSpent: Duration(seconds: json['timeSpent']),
    );
  }
}
