import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '/models/quiz_history.dart';
import '/models/question.dart';

class QuizHistoryController extends GetxController {
  final RxList<QuizHistory> _quizHistory = <QuizHistory>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _selectedFilter = 'All'.obs;
  final RxString _selectedSortBy = 'Date'.obs;

  // Getters
  List<QuizHistory> get quizHistory => _quizHistory;
  bool get isLoading => _isLoading.value;
  String get selectedFilter => _selectedFilter.value;
  String get selectedSortBy => _selectedSortBy.value;

  // Filter options
  final List<String> filterOptions = ['All', 'Daily Quiz', 'General Knowledge', 'Science', 'History', 'Geography', 'Sports'];
  final List<String> sortOptions = ['Date', 'Score', 'Accuracy', 'Category'];

  @override
  void onInit() {
    super.onInit();
    loadQuizHistory();
  }

  // Load quiz history from SharedPreferences
  Future<void> loadQuizHistory() async {
    _isLoading.value = true;
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getStringList('quiz_history') ?? [];
      
      _quizHistory.value = historyJson
          .map((json) => QuizHistory.fromJson(jsonDecode(json)))
          .toList();
      
      _sortHistory();
    } catch (e) {
      print('Error loading quiz history: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  // Save quiz history to SharedPreferences
  Future<void> saveQuizHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = _quizHistory
          .map((history) => jsonEncode(history.toJson()))
          .toList();
      
      await prefs.setStringList('quiz_history', historyJson);
    } catch (e) {
      print('Error saving quiz history: $e');
    }
  }

  // Add new quiz result to history
  Future<void> addQuizResult({
    required String category,
    required int score,
    required int totalQuestions,
    required Duration timeTaken,
    required List<Question> questions,
    required List<int?> userAnswers,
    bool isDailyQuiz = false,
    int coinsEarned = 0,
  }) async {
    try {
      // Create question results
      final questionResults = <QuestionResult>[];
      for (int i = 0; i < questions.length; i++) {
        final question = questions[i];
        final userAnswer = userAnswers.length > i ? userAnswers[i] : null;
        
        questionResults.add(QuestionResult(
          question: question.question,
          options: question.options,
          correctAnswerIndex: question.correctAnswerIndex,
          selectedAnswerIndex: userAnswer,
          isCorrect: userAnswer == question.correctAnswerIndex,
          timeSpent: Duration(seconds: (timeTaken.inSeconds / questions.length).round()),
        ));
      }

      // Create quiz history entry
      final quizHistory = QuizHistory(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        category: category,
        score: score,
        totalQuestions: totalQuestions,
        accuracy: (score / totalQuestions) * 100,
        completedAt: DateTime.now(),
        timeTaken: timeTaken,
        isDailyQuiz: isDailyQuiz,
        coinsEarned: coinsEarned,
        questionResults: questionResults,
      );

      // Add to history (newest first)
      _quizHistory.insert(0, quizHistory);
      
      // Keep only last 100 entries to prevent storage bloat
      if (_quizHistory.length > 100) {
        _quizHistory.removeRange(100, _quizHistory.length);
      }

      await saveQuizHistory();
    } catch (e) {
      print('Error adding quiz result: $e');
    }
  }

  // Get filtered and sorted history
  List<QuizHistory> getFilteredHistory() {
    var filtered = _quizHistory.where((history) {
      if (_selectedFilter.value == 'All') return true;
      if (_selectedFilter.value == 'Daily Quiz') return history.isDailyQuiz;
      return history.category == _selectedFilter.value;
    }).toList();

    // Sort based on selected option
    switch (_selectedSortBy.value) {
      case 'Score':
        filtered.sort((a, b) => b.score.compareTo(a.score));
        break;
      case 'Accuracy':
        filtered.sort((a, b) => b.accuracy.compareTo(a.accuracy));
        break;
      case 'Category':
        filtered.sort((a, b) => a.category.compareTo(b.category));
        break;
      case 'Date':
      default:
        filtered.sort((a, b) => b.completedAt.compareTo(a.completedAt));
        break;
    }

    return filtered;
  }

  // Update filter
  void updateFilter(String filter) {
    _selectedFilter.value = filter;
  }

  // Update sort option
  void updateSortBy(String sortBy) {
    _selectedSortBy.value = sortBy;
    _sortHistory();
  }

  // Sort history based on current sort option
  void _sortHistory() {
    switch (_selectedSortBy.value) {
      case 'Score':
        _quizHistory.sort((a, b) => b.score.compareTo(a.score));
        break;
      case 'Accuracy':
        _quizHistory.sort((a, b) => b.accuracy.compareTo(a.accuracy));
        break;
      case 'Category':
        _quizHistory.sort((a, b) => a.category.compareTo(b.category));
        break;
      case 'Date':
      default:
        _quizHistory.sort((a, b) => b.completedAt.compareTo(a.completedAt));
        break;
    }
  }

  // Get statistics
  Map<String, dynamic> getStatistics() {
    if (_quizHistory.isEmpty) {
      return {
        'totalQuizzes': 0,
        'averageScore': 0.0,
        'averageAccuracy': 0.0,
        'bestScore': 0,
        'totalTimePlayed': Duration.zero,
        'favoriteCategory': 'None',
        'perfectScores': 0,
      };
    }

    final totalQuizzes = _quizHistory.length;
    final totalScore = _quizHistory.fold<int>(0, (sum, quiz) => sum + quiz.score);
    final totalAccuracy = _quizHistory.fold<double>(0, (sum, quiz) => sum + quiz.accuracy);
    final bestScore = _quizHistory.map((quiz) => quiz.score).reduce((a, b) => a > b ? a : b);
    final totalTime = _quizHistory.fold<Duration>(Duration.zero, (sum, quiz) => sum + quiz.timeTaken);
    final perfectScores = _quizHistory.where((quiz) => quiz.accuracy == 100.0).length;

    // Find favorite category
    final categoryCount = <String, int>{};
    for (final quiz in _quizHistory) {
      categoryCount[quiz.category] = (categoryCount[quiz.category] ?? 0) + 1;
    }
    final favoriteCategory = categoryCount.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    return {
      'totalQuizzes': totalQuizzes,
      'averageScore': totalScore / totalQuizzes,
      'averageAccuracy': totalAccuracy / totalQuizzes,
      'bestScore': bestScore,
      'totalTimePlayed': totalTime,
      'favoriteCategory': favoriteCategory,
      'perfectScores': perfectScores,
    };
  }

  // Clear all history
  Future<void> clearHistory() async {
    _quizHistory.clear();
    await saveQuizHistory();
  }

  // Delete specific quiz from history
  Future<void> deleteQuiz(String quizId) async {
    _quizHistory.removeWhere((quiz) => quiz.id == quizId);
    await saveQuizHistory();
  }
}
