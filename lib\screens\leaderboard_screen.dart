import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '/controllers/data_controller.dart';
import 'dart:math' as math;

class LeaderboardScreen extends StatefulWidget {
  const LeaderboardScreen({super.key});

  @override
  State<LeaderboardScreen> createState() => _LeaderboardScreenState();
}

class _LeaderboardScreenState extends State<LeaderboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _pulseController;
  late AnimationController _shimmerController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _shimmerAnimation;

  String _selectedTimeFrame = 'All Time';
  final List<String> _timeFrames = ['All Time', 'This Week', 'This Month'];

  // Dynamic leaderboard data that includes the current user
  List<Map<String, dynamic>> _leaderboardData = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _shimmerAnimation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _shimmerController,
      curve: Curves.easeInOut,
    ));

    _generateDynamicLeaderboard();
    _animationController.forward();
    _pulseController.repeat(reverse: true);
    _shimmerController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pulseController.dispose();
    _shimmerController.dispose();
    super.dispose();
  }

  void _generateDynamicLeaderboard() {
    final DataController dataController = Get.find<DataController>();

    // Sample players with varied scores
    final List<Map<String, dynamic>> samplePlayers = [
      {'name': 'Quiz Master', 'score': 2500, 'level': 25, 'avatar': '🏆', 'streak': 15, 'accuracy': 95},
      {'name': 'Brain Genius', 'score': 2200, 'level': 22, 'avatar': '🧠', 'streak': 12, 'accuracy': 92},
      {'name': 'Knowledge King', 'score': 2000, 'level': 20, 'avatar': '👑', 'streak': 10, 'accuracy': 88},
      {'name': 'Smart Cookie', 'score': 1800, 'level': 18, 'avatar': '🍪', 'streak': 8, 'accuracy': 85},
      {'name': 'Trivia Expert', 'score': 1600, 'level': 16, 'avatar': '🎯', 'streak': 6, 'accuracy': 82},
      {'name': 'Quiz Ninja', 'score': 1400, 'level': 14, 'avatar': '🥷', 'streak': 5, 'accuracy': 78},
      {'name': 'Study Buddy', 'score': 1200, 'level': 12, 'avatar': '📚', 'streak': 4, 'accuracy': 75},
      {'name': 'Fast Learner', 'score': 1000, 'level': 10, 'avatar': '⚡', 'streak': 3, 'accuracy': 72},
      {'name': 'Quiz Rookie', 'score': 800, 'level': 8, 'avatar': '🌟', 'streak': 2, 'accuracy': 68},
      {'name': 'New Player', 'score': 600, 'level': 6, 'avatar': '🎮', 'streak': 1, 'accuracy': 65},
    ];

    // Add current user to the list if they have a score
    if (dataController.totalScore.value > 0 && dataController.userName.value.isNotEmpty) {
      samplePlayers.add({
        'name': dataController.userName.value,
        'score': dataController.totalScore.value,
        'level': dataController.getUserLevel(),
        'avatar': '👤',
        'streak': dataController.dailyQuizStreak.value,
        'accuracy': dataController.gamesPlayed.value > 0
            ? (dataController.averageScore.value * 10).round()
            : 0,
        'isCurrentUser': true,
      });
    }

    // Sort by score and assign ranks
    samplePlayers.sort((a, b) => b['score'].compareTo(a['score']));

    setState(() {
      _leaderboardData = samplePlayers;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final DataController dataController = Get.find<DataController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Leaderboard'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list),
            onSelected: (value) {
              setState(() {
                _selectedTimeFrame = value;
                _isLoading = true;
              });
              // Simulate loading delay
              Future.delayed(const Duration(milliseconds: 500), () {
                _generateDynamicLeaderboard();
              });
            },
            itemBuilder: (context) => _timeFrames.map((timeFrame) {
              return PopupMenuItem<String>(
                value: timeFrame,
                child: Row(
                  children: [
                    Icon(
                      _selectedTimeFrame == timeFrame
                          ? Icons.radio_button_checked
                          : Icons.radio_button_unchecked,
                      color: Colors.blue,
                    ),
                    const SizedBox(width: 8),
                    Text(timeFrame),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.blue.shade400, Colors.blue.shade900],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: _isLoading
            ? _buildLoadingState()
            : FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Column(
                    children: [
                      // Time Frame Indicator
                      Container(
                        margin: const EdgeInsets.all(16),
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          _selectedTimeFrame,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ),

                      // User's Current Position Card
                      _buildUserPositionCard(dataController),

                      // Leaderboard List
                      Expanded(
                        child: ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: _leaderboardData.length,
                          itemBuilder: (context, index) {
                            final player = _leaderboardData[index];
                            return AnimatedContainer(
                              duration: Duration(milliseconds: 300 + (index * 100)),
                              margin: const EdgeInsets.only(bottom: 12),
                              child: _buildLeaderboardItem(
                                rank: index + 1,
                                name: player['name'],
                                score: player['score'],
                                level: player['level'],
                                avatar: player['avatar'],
                                streak: player['streak'] ?? 0,
                                accuracy: player['accuracy'] ?? 0,
                                isCurrentUser: player['isCurrentUser'] ?? false,
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedBuilder(
            animation: _shimmerAnimation,
            builder: (context, child) {
              return Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      Colors.white.withValues(alpha: 0.1),
                      Colors.white.withValues(alpha: 0.3),
                      Colors.white.withValues(alpha: 0.1),
                    ],
                    stops: [
                      _shimmerAnimation.value - 0.3,
                      _shimmerAnimation.value,
                      _shimmerAnimation.value + 0.3,
                    ].map((e) => e.clamp(0.0, 1.0)).toList(),
                  ),
                ),
                child: const Icon(
                  Icons.leaderboard,
                  color: Colors.white,
                  size: 30,
                ),
              );
            },
          ),
          const SizedBox(height: 16),
          const Text(
            'Loading Leaderboard...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserPositionCard(DataController dataController) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.amber.shade400, Colors.orange.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.amber.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white24,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Obx(() => Text(
                        dataController.userName.value.isNotEmpty
                            ? dataController.userName.value
                            : 'Guest User',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      )),
                      const SizedBox(height: 4),
                      Obx(() => Text(
                        'Rank #${_getUserRank(dataController)} • ${dataController.totalScore.value} pts',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      )),
                    ],
                  ),
                ),
                Obx(() => Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white24,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Level ${dataController.getUserLevel()}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                )),
              ],
            ),
          ),
        );
      },
    );
  }

  int _getUserRank(DataController dataController) {
    if (dataController.totalScore.value == 0) return 999;

    int rank = 1;
    for (var player in _leaderboardData) {
      if (player['score'] > dataController.totalScore.value) {
        rank++;
      }
    }
    return rank;
  }

  Widget _buildLeaderboardItem({
    required int rank,
    required String name,
    required int score,
    required int level,
    required String avatar,
    int streak = 0,
    int accuracy = 0,
    bool isCurrentUser = false,
  }) {
    Color getRankColor() {
      switch (rank) {
        case 1:
          return Colors.amber;
        case 2:
          return Colors.grey.shade400;
        case 3:
          return Colors.brown;
        default:
          return isCurrentUser ? Colors.green : Colors.blue;
      }
    }

    IconData getRankIcon() {
      switch (rank) {
        case 1:
          return Icons.emoji_events;
        case 2:
          return Icons.military_tech;
        case 3:
          return Icons.workspace_premium;
        default:
          return Icons.person;
      }
    }

    return Card(
      elevation: rank <= 3 ? 12 : (isCurrentUser ? 8 : 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: rank <= 3
              ? LinearGradient(
                  colors: [
                    getRankColor().withValues(alpha: 0.1),
                    getRankColor().withValues(alpha: 0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : isCurrentUser
                  ? LinearGradient(
                      colors: [
                        Colors.green.withValues(alpha: 0.1),
                        Colors.green.withValues(alpha: 0.05),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    )
                  : null,
          border: isCurrentUser
              ? Border.all(color: Colors.green, width: 2)
              : null,
        ),
        child: ExpansionTile(
          tilePadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 8,
          ),
          leading: Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: getRankColor(),
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: getRankColor().withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (rank <= 3)
                  Icon(
                    getRankIcon(),
                    color: Colors.white,
                    size: 20,
                  )
                else
                  Text(
                    rank.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
              ],
            ),
          ),
          title: Row(
            children: [
              Text(
                avatar,
                style: const TextStyle(fontSize: 20),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  name,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: isCurrentUser
                        ? Colors.green
                        : (rank <= 3 ? getRankColor() : Theme.of(context).colorScheme.onSurface),
                  ),
                ),
              ),
              if (isCurrentUser)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'YOU',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          subtitle: Row(
            children: [
              Text(
                'Level $level',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  fontSize: 14,
                ),
              ),
              if (streak > 0) ...[
                const SizedBox(width: 8),
                Icon(
                  Icons.local_fire_department,
                  color: Colors.orange,
                  size: 16,
                ),
                Text(
                  ' $streak',
                  style: TextStyle(
                    color: Colors.orange,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ],
          ),
          trailing: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '$score',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                  color: isCurrentUser
                      ? Colors.green
                      : (rank <= 3 ? getRankColor() : Colors.blue),
                ),
              ),
              Text(
                'points',
                style: TextStyle(
                  fontSize: 12,
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('Accuracy', '$accuracy%', Icons.track_changes),
                  _buildStatItem('Streak', '$streak', Icons.local_fire_department),
                  _buildStatItem('Level', '$level', Icons.star),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: Theme.of(context).colorScheme.primary,
            size: 20,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
      ],
    );
  }
}
